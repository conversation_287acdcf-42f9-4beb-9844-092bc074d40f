const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createClient = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '客户名称不能为空',
      'any.required': '客户名称是必填项'
    }),
    code: Joi.string().required().messages({
      'string.empty': '客户编号不能为空',
      'any.required': '客户编号是必填项'
    }),
    type: Joi.string().allow(null, ''),
    status: Joi.string().allow(null, ''),
    description: Joi.string().allow(null, ''),
    contactName: Joi.string().allow(null, ''),
    contactTitle: Joi.string().allow(null, ''),
    phone: Joi.string().allow(null, ''),
    email: Joi.string().email().allow(null, '').messages({
      'string.email': '电子邮箱格式不正确'
    }),
    address: Joi.string().allow(null, ''),
    taxId: Joi.string().allow(null, ''),
    bankAccount: Joi.string().allow(null, ''),
    bankName: Joi.string().allow(null, ''),
    bankCode: Joi.string().allow(null, ''),
    source: Joi.string().allow(null, ''),
    industry: Joi.string().allow(null, ''),
    notes: Joi.string().allow(null, '')
  })
};

const getClient = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  })
};

const updateClient = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '客户名称不能为空'
    }),
    code: Joi.string().messages({
      'string.empty': '客户编号不能为空'
    }),
    type: Joi.string().allow(null, ''),
    status: Joi.string().allow(null, ''),
    description: Joi.string().allow(null, ''),
    contactName: Joi.string().allow(null, ''),
    contactTitle: Joi.string().allow(null, ''),
    phone: Joi.string().allow(null, ''),
    email: Joi.string().email().allow(null, '').messages({
      'string.email': '电子邮箱格式不正确'
    }),
    address: Joi.string().allow(null, ''),
    taxId: Joi.string().allow(null, ''),
    bankAccount: Joi.string().allow(null, ''),
    bankName: Joi.string().allow(null, ''),
    bankCode: Joi.string().allow(null, ''),
    source: Joi.string().allow(null, ''),
    industry: Joi.string().allow(null, ''),
    notes: Joi.string().allow(null, '')
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteClient = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  })
};

const getClients = {
  query: Joi.object().keys({
    type: Joi.string().allow(null),
    status: Joi.string().allow(null),
    search: Joi.string().allow(null),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
    limit: Joi.number().integer().messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数'
    }),
    sortBy: Joi.string().allow(null),
    sortOrder: Joi.string().valid('asc', 'desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

module.exports = {
  createClient,
  getClient,
  updateClient,
  deleteClient,
  getClients
};
