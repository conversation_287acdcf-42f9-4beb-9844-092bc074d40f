const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createFinance = {
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    type: Joi.string().valid('income', 'expense').required().messages({
      'string.empty': '财务类型不能为空',
      'any.only': '财务类型必须是收入(income)或支出(expense)',
      'any.required': '财务类型是必填项'
    }),
    category: Joi.string().required().messages({
      'string.empty': '类别不能为空',
      'any.required': '类别是必填项'
    }),
    amount: Joi.number().positive().required().messages({
      'number.base': '金额必须是数字',
      'number.positive': '金额必须是正数',
      'any.required': '金额是必填项'
    }),
    currency: Joi.string().default('USD').messages({
      'string.empty': '货币不能为空'
    }),
    date: Joi.date().default(new Date()).messages({
      'date.base': '日期格式不正确'
    }),
    description: Joi.string().allow('', null),
    reference: Joi.string().allow('', null),
    paymentMethod: Joi.string().allow('', null),
    status: Joi.string().valid('pending', 'completed', 'cancelled').default('pending').messages({
      'any.only': '状态必须是待处理(pending)、已完成(completed)或已取消(cancelled)'
    }),
    attachments: Joi.array().items(Joi.string()),
    buyerInfo: Joi.string().allow('', null),
    invoiceDate: Joi.string().allow('', null),
    invoiceTaxRate: Joi.number().allow(null),
    invoiceType: Joi.string().allow('', null),
    incomePayments: Joi.array().items(Joi.string()),
    sellerInfo: Joi.string().allow('', null),
    inputInvoiceDate: Joi.string().allow('', null),
    inputInvoiceTaxRate: Joi.number().allow(null),
    inputInvoiceType: Joi.string().allow('', null),
    subcontractPayment: Joi.number().allow(null),
    purchasePayment: Joi.number().allow(null),
    reimbursementPayment: Joi.number().allow(null),
    netIncome: Joi.number().allow(null)
  })
};

const getFinance = {
  params: Joi.object().keys({
    financeId: Joi.string().custom(objectId).required().messages({
      'string.empty': '财务记录ID不能为空',
      'any.required': '财务记录ID是必填项'
    })
  })
};

const updateFinance = {
  params: Joi.object().keys({
    financeId: Joi.string().custom(objectId).required().messages({
      'string.empty': '财务记录ID不能为空',
      'any.required': '财务记录ID是必填项'
    })
  }),
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    type: Joi.string().valid('income', 'expense').messages({
      'string.empty': '财务类型不能为空',
      'any.only': '财务类型必须是收入(income)或支出(expense)'
    }),
    category: Joi.string().messages({
      'string.empty': '类别不能为空'
    }),
    amount: Joi.number().positive().messages({
      'number.base': '金额必须是数字',
      'number.positive': '金额必须是正数'
    }),
    currency: Joi.string().messages({
      'string.empty': '货币不能为空'
    }),
    date: Joi.date().messages({
      'date.base': '日期格式不正确'
    }),
    description: Joi.string().allow('', null),
    reference: Joi.string().allow('', null),
    paymentMethod: Joi.string().allow('', null),
    status: Joi.string().valid('pending', 'completed', 'cancelled').messages({
      'any.only': '状态必须是待处理(pending)、已完成(completed)或已取消(cancelled)'
    }),
    attachments: Joi.array().items(Joi.string()),
    buyerInfo: Joi.string().allow('', null),
    invoiceDate: Joi.string().allow('', null),
    invoiceTaxRate: Joi.number().allow(null),
    invoiceType: Joi.string().allow('', null),
    incomePayments: Joi.array(),
    sellerInfo: Joi.string().allow('', null),
    inputInvoiceDate: Joi.string().allow('', null),
    inputInvoiceTaxRate: Joi.number().allow(null),
    inputInvoiceType: Joi.string().allow('', null),
    subcontractPayment: Joi.number().allow(null),
    purchasePayment: Joi.number().allow(null),
    reimbursementPayment: Joi.number().allow(null),
    netIncome: Joi.number().allow(null)
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteFinance = {
  params: Joi.object().keys({
    financeId: Joi.string().custom(objectId).required().messages({
      'string.empty': '财务记录ID不能为空',
      'any.required': '财务记录ID是必填项'
    })
  })
};

const getFinances = {
  query: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    type: Joi.string().valid('income', 'expense').messages({
      'any.only': '财务类型必须是收入(income)或支出(expense)'
    }),
    category: Joi.string().allow(null),
    status: Joi.string().valid('pending', 'completed', 'cancelled').messages({
      'any.only': '状态必须是待处理(pending)、已完成(completed)或已取消(cancelled)'
    }),
    search: Joi.string().allow(null),
    dateFrom: Joi.date().messages({
      'date.base': '开始日期格式不正确'
    }),
    dateTo: Joi.date().messages({
      'date.base': '结束日期格式不正确'
    }),
    minAmount: Joi.number().min(0).messages({
      'number.base': '最小金额必须是数字',
      'number.min': '最小金额不能小于0'
    }),
    maxAmount: Joi.number().min(0).messages({
      'number.base': '最大金额必须是数字',
      'number.min': '最大金额不能小于0'
    }),
    sortBy: Joi.string().default('date'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于1'
    }),
    limit: Joi.number().integer().min(1).max(100).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100'
    })
  })
};

module.exports = {
  createFinance,
  getFinance,
  updateFinance,
  deleteFinance,
  getFinances
}; 