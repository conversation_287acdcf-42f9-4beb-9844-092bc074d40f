<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <h1 class="page-title">{{ isEditMode ? '编辑采购单' : '新建采购单' }}</h1>
      </div>
      <div class="flex space-x-4">
        <router-link to="/purchases" class="btn btn-secondary flex items-center text-sm">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
                    返回列表
        </router-link>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading && !error" class="card shadow-md p-8 text-center">
      <div class="flex justify-center">
        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <p class="mt-4 text-gray-600">正在加载数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>
    
    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 表单区域 -->
    <div v-if="!isLoading || error" class="card shadow-md">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="purchase-number" class="form-label">采购单号</label>
              <input
                type="text"
                id="purchase-number"
                v-model="purchaseForm.number"
                class="form-input"
                placeholder="系统自动生成"
                disabled
              />
            </div>
            <div>
              <label for="purchase-date" class="form-label">采购日期 <span class="form-required">*</span></label>
              <input
                type="date"
                id="purchase-date"
                v-model="purchaseForm.date"
                class="form-input"
                required
                max="3000-12-31"
              />
            </div>
            <div>
              <label for="supplier" class="form-label">供应商 <span class="form-required">*</span></label>
              <select id="supplier" v-model="purchaseForm.supplierId" class="form-input" required>
                <option value="" disabled>-- 选择供应商 --</option>
                <option v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id">
                  {{ supplier.name }}
                </option>
              </select>
            </div>
            <div>
              <label for="status" class="form-label">状态</label>
              <select id="status" v-model="purchaseForm.status" class="form-input">
                <option value="draft">草稿</option>
                <option value="pending">待审核</option>
                <option value="approved">已审核</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 采购项目 -->
        <div class="form-section">
          <h2 class="form-section-title">采购项目</h2>

          <!-- 采购项目表格 -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物品</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(item, index) in purchaseForm.items" :key="index" class="hover:bg-gray-50">
                  <td class="px-4 py-4 whitespace-nowrap">
                    <select v-model="item.productId" class="form-input py-1" required @change="handleProductChange(index)">
                      <option value="" disabled>-- 选择物品 --</option>
                      <option v-for="product in products" :key="product.id" :value="product.id">
                        {{ product.name }}
                      </option>
                    </select>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input type="text" v-model="item.specification" class="form-input py-1" placeholder="规格" />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      v-model.number="item.quantity"
                      @input="calculateItemAmount(index)"
                      class="form-input py-1 w-24"
                      min="1"
                      required
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <input
                      type="number"
                      v-model.number="item.price"
                      @input="calculateItemAmount(index)"
                      class="form-input py-1 w-32"
                      min="0"
                      step="0.01"
                      required
                    />
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <span class="text-gray-700">¥{{ formatCurrency(item.amount) }}</span>
                  </td>
                  <td class="px-4 py-4 whitespace-nowrap">
                    <button
                      type="button"
                      @click="removeItem(index)"
                      class="text-red-600 hover:text-red-900"
                      title="删除项目"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 添加项目按钮 -->
          <div class="mt-4">
            <button
              type="button"
              @click="addItem"
              class="btn btn-secondary flex items-center text-sm"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加项目
            </button>
          </div>

          <!-- 总计 -->
          <div class="mt-6 text-right">
            <div class="inline-block bg-gray-50 rounded-lg p-4">
              <div class="text-gray-600">总金额: <span class="text-xl font-bold text-gray-800 ml-2">¥{{ formatCurrency(totalAmount) }}</span></div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <h2 class="form-section-title">备注信息</h2>
          <div>
            <label for="remarks" class="form-label">备注</label>
            <textarea
              id="remarks"
              v-model="purchaseForm.remarks"
              class="form-input"
              rows="3"
              placeholder="添加采购单备注信息"
            ></textarea>
          </div>
        </div>
        <!-- 底部操作按钮 -->
        <div class="flex justify-end space-x-4 mt-6">
          <button type="button" @click="resetForm" class="btn btn-secondary">重置</button>
          <button type="submit" @click="submitForm" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isLoading">
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
               {{ isEditMode ? '保存修改' : '保存采购单' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 判断是编辑模式还是新增模式
const isEditMode = computed(() => !!route.params.id);
const purchaseId = computed(() => route.params.id);

// 数据状态
const suppliers = ref([]);
const products = ref([]);
const isLoading = ref(false);
const error = ref(null);
const successMessage = ref('');

// 获取供应商列表
async function fetchSuppliers() {
  try {
    const response = await axios.get('/api/suppliers');
    suppliers.value = response.data.results || [];
  } catch (err) {
    console.error('获取供应商列表失败:', err);
    error.value = '很抱歉，无法获取供应商列表信息。请检查您的网络连接，或稍后再试。如需帮助，请联系系统管理员。';
  }
}

// 获取产品列表
async function fetchProducts() {
  try {
    const response = await axios.get('/api/products');
    products.value = response.data.results || [];
  } catch (err) {
    console.error('获取产品列表失败:', err);
    error.value = '很抱歉，无法获取产品列表信息。请检查您的网络连接，或稍后再试。如需帮助，请联系系统管理员。';
  }
}

// 表单数据 - 与后端模型字段保持一致
const purchaseForm = ref({
  // 后端模型字段
  purchaseNumber: '系统生成', // 对应前端的 number
  purchaseDate: new Date().toISOString().substr(0, 10), // 对应前端的 date
  supplierId: '',
  status: 'draft',
  totalAmount: 0, // 总金额，由前端计算
  currency: 'CNY', // 默认货币
  items: [createEmptyItem()],
  notes: '', // 对应前端的 remarks

  // 可选字段
  projectId: null,
  expectedDeliveryDate: null,
  deliveryAddress: '',

  // 前端特有字段（不在后端模型中）
  number: '系统生成', // 临时存储，提交时会转换为 purchaseNumber
  date: new Date().toISOString().substr(0, 10), // 临时存储，提交时会转换为 purchaseDate
  remarks: '', // 临时存储，提交时会转换为 notes
});

// 创建空的采购项目 - 与后端模型字段保持一致
function createEmptyItem() {
  return {
    // 后端模型字段
    name: '',
    quantity: 1,
    unit: '个',
    unitPrice: 0, // 对应前端的 price
    totalPrice: 0, // 对应前端的 amount
    description: '', // 对应前端的 specification

    // 前端特有字段（不在后端模型中）
    productId: '', // 用于前端选择产品
    specification: '', // 临时存储，提交时会转换为 description
    price: 0, // 临时存储，提交时会转换为 unitPrice
    amount: 0 // 临时存储，提交时会转换为 totalPrice
  };
}

// 添加采购项目
function addItem() {
  purchaseForm.value.items.push(createEmptyItem());
}

// 移除采购项目
function removeItem(index) {
  if (purchaseForm.value.items.length > 1) {
    purchaseForm.value.items.splice(index, 1);
  }
}

// 计算单项金额
function calculateItemAmount(index) {
  const item = purchaseForm.value.items[index];
  // 更新前端字段
  item.amount = item.quantity * item.price;

  // 同步更新后端字段
  item.unitPrice = item.price;
  item.totalPrice = item.amount;

  // 更新采购单总金额
  purchaseForm.value.totalAmount = totalAmount.value;
}

// 计算总金额
const totalAmount = computed(() => {
  return purchaseForm.value.items.reduce((sum, item) => sum + (item.amount || 0), 0);
});

// 货币格式化
function formatCurrency(value) {
  return value.toFixed(2);
}

// 重置表单
function resetForm() {
  if (isEditMode.value) {
    // 如果是编辑模式，重新获取原始数据
    fetchPurchaseDetail();
  } else {
    // 如果是新建模式，清空表单
    purchaseForm.value = {
      // 后端模型字段
      purchaseNumber: '系统生成',
      purchaseDate: new Date().toISOString().substr(0, 10),
      supplierId: '',
      status: 'draft',
      totalAmount: 0,
      currency: 'CNY',
      items: [createEmptyItem()],
      notes: '',

      // 可选字段
      projectId: null,
      expectedDeliveryDate: null,
      deliveryAddress: '',

      // 前端特有字段
      number: '系统生成',
      date: new Date().toISOString().substr(0, 10),
      remarks: ''
    };
  }
}

// 提交表单
async function submitForm() {
  try {
    isLoading.value = true;
    error.value = null;

    // 验证表单
    if (!purchaseForm.value.supplierId) {
      error.value = '请选择一个供应商。采购单需要关联到特定供应商才能继续。';
      return;
    }

    if (purchaseForm.value.items.length === 0) {
      error.value = '请添加至少一个采购项目。采购单需要包含具体的采购物品信息才能提交。';
      return;
    }

    // 验证每个采购项目
    for (let i = 0; i < purchaseForm.value.items.length; i++) {
      const item = purchaseForm.value.items[i];
      if (!item.productId) {
        error.value = `请为第 ${i+1} 个采购项目选择具体产品。每个采购项目都需要明确的产品信息。`;
        return;
      }
      if (item.quantity <= 0) {
        error.value = `第 ${i+1} 个采购项目的数量必须大于0。请输入有效的采购数量。`;
        return;
      }
      if (item.price < 0) {
        error.value = `第 ${i+1} 个采购项目的价格不能为负数。请输入有效的单价金额。`;
        return;
      }
    }

    // 准备提交数据，直接使用后端模型字段
    // 处理采购项目，确保每个项目都有必要的字段
    const formattedItems = purchaseForm.value.items.map(item => {
      // 查找产品信息
      const product = products.value.find(p => p.id === item.productId) || {};

      // 更新项目的后端字段
      item.name = product.name || '未知产品';
      item.unit = product.unit || '个';
      item.description = item.specification || '';

      // 返回符合后端模型的项目数据
      return {
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        description: item.description
      };
    });

    // 构建提交数据，使用后端模型字段
    const purchaseData = {
      // 必要字段
      purchaseNumber: purchaseForm.value.purchaseNumber !== '系统生成' ?
                      purchaseForm.value.purchaseNumber :
                      `PO-${Date.now()}`,
      supplierId: purchaseForm.value.supplierId,
      status: purchaseForm.value.status,
      totalAmount: purchaseForm.value.totalAmount,
      currency: purchaseForm.value.currency,
      purchaseDate: purchaseForm.value.purchaseDate,
      items: formattedItems,
      notes: purchaseForm.value.notes,

      // 可选字段
      projectId: purchaseForm.value.projectId || null,
      expectedDeliveryDate: purchaseForm.value.expectedDeliveryDate || null,
      deliveryAddress: purchaseForm.value.deliveryAddress || ''
    };

    // 提交表单数据
    if (isEditMode.value) {
      // 编辑模式：调用更新API
      await axios.patch(`/api/purchases/${purchaseId.value}`, purchaseData);
      console.log('采购单更新成功!');
      successMessage.value = '采购单更新成功!';
    } else {
      // 新增模式：调用创建API
      await axios.post('/api/purchases', purchaseData);
      console.log('采购单创建成功!');
      successMessage.value = '采购单创建成功!';
    }

    // 延迟一小时后跳转到采购单列表页面
    setTimeout(() => {
      router.push('/purchases');
    }, 1500);
  } catch (err) {
    console.error('提交采购单失败:', err);
    error.value = err.response?.data?.message || '很抱歉，提交采购单时遇到了问题。请检查您的网络连接和输入信息是否正确，然后再次尝试提交。如果问题持续存在，请联系系统管理员获取帮助。';
  } finally {
    isLoading.value = false;
  }
}

// 获取采购单详情
async function fetchPurchaseDetail() {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await axios.get(`/api/purchases/${purchaseId.value}/`);  // Adding trailing slash to ensure proper routing
    const purchaseData = response.data;

    // 填充表单数据
    purchaseForm.value = {
      // 后端模型字段
      purchaseNumber: purchaseData.purchaseNumber || '系统生成',
      purchaseDate: purchaseData.purchaseDate || new Date().toISOString().substr(0, 10),
      supplierId: purchaseData.supplierId || '',
      status: purchaseData.status || 'draft',
      totalAmount: purchaseData.totalAmount || 0,
      currency: purchaseData.currency || 'CNY',
      items: [],
      notes: purchaseData.notes || '',

      // 可选字段
      projectId: purchaseData.projectId || null,
      expectedDeliveryDate: purchaseData.expectedDeliveryDate || null,
      deliveryAddress: purchaseData.deliveryAddress || '',

      // 前端特有字段
      number: purchaseData.purchaseNumber || '系统生成',
      date: purchaseData.purchaseDate || new Date().toISOString().substr(0, 10),
      remarks: purchaseData.notes || ''
    };

    // 处理采购项目
    if (purchaseData.items && purchaseData.items.length > 0) {
      purchaseForm.value.items = purchaseData.items.map(item => {
        return {
          // 后端模型字段
          name: item.name || '',
          quantity: item.quantity || 1,
          unit: item.unit || '个',
          unitPrice: item.unitPrice || 0,
          totalPrice: item.totalPrice || 0,
          description: item.description || '',

          // 前端特有字段
          productId: item.productId || '',
          specification: item.description || '',
          price: item.unitPrice || 0,
          amount: item.totalPrice || 0
        };
      });
    } else {
      purchaseForm.value.items = [createEmptyItem()];
    }
  } catch (err) {
    console.error('获取采购单详情失败:', err);
    error.value = err.response?.data?.message || '很抱歉，无法获取采购单详细信息。可能是网络连接问题或该采购单不存在。请检查网络连接后再试，或返回列表页查看其他采购单。';
  } finally {
    isLoading.value = false;
  }
}

// 处理产品选择变化
function handleProductChange(index) {
  const item = purchaseForm.value.items[index];
  const selectedProduct = products.value.find(p => p.id === item.productId);
  
  if (selectedProduct) {
    // 设置产品单价
    item.price = selectedProduct.price;
    // 设置产品单位
    item.unit = selectedProduct.unit;
    // 重新计算金额
    calculateItemAmount(index);
  }
}

onMounted(async () => {
  console.log(isEditMode.value ? '采购单编辑页面已加载' : '采购单创建页面已加载');
  isLoading.value = true;

  try {
    // 并行加载数据
    await Promise.all([
      fetchSuppliers(),
      fetchProducts()
    ]);

    // 如果是编辑模式，获取采购单详情
    if (isEditMode.value) {
      await fetchPurchaseDetail();
    }
  } catch (err) {
    console.error('加载数据失败:', err);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-200;
}

.back-button {
  @apply text-gray-600 hover:text-gray-900 focus:outline-none;
}

.form-required {
  @apply text-red-500;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>