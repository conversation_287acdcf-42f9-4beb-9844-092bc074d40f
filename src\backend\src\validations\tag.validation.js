const Joi = require('joi');

const createTagSchema = {
  body: Joi.object().keys({
    name: Joi.string().required().min(1).max(50).messages({
      'string.empty': '标签名称不能为空',
      'string.min': '标签名称至少需要{#limit}个字符',
      'string.max': '标签名称不能超过{#limit}个字符',
      'any.required': '标签名称是必填项'
    }),
    description: Joi.string().allow('', null),
    color: Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).default('#3498db').messages({
      'string.pattern.base': '颜色必须是有效的十六进制颜色代码（例如，#3498db）'
    })
  })
};

const updateTagSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '标签编号不能为空',
      'string.guid': '标签编号必须是有效的UUID格式',
      'any.required': '标签编号是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().min(1).max(50).messages({
      'string.empty': '标签名称不能为空',
      'string.min': '标签名称至少需要{#limit}个字符',
      'string.max': '标签名称不能超过{#limit}个字符'
    }),
    description: Joi.string().allow('', null),
    color: Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).messages({
      'string.pattern.base': '颜色必须是有效的十六进制颜色代码（例如，#3498db）'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const getTagSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '标签编号不能为空',
      'string.guid': '标签编号必须是有效的UUID格式',
      'any.required': '标签编号是必填项'
    })
  })
};

const deleteTagSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '标签编号不能为空',
      'string.guid': '标签编号必须是有效的UUID格式',
      'any.required': '标签编号是必填项'
    })
  })
};

const getTagsSchema = {
  query: Joi.object().keys({
    search: Joi.string().allow('', null),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().valid('name', 'createdAt').default('name').messages({
      'any.only': '排序字段必须是名称(name)或创建时间(createdAt)'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

module.exports = {
  createTagSchema,
  updateTagSchema,
  getTagSchema,
  deleteTagSchema,
  getTagsSchema
};