<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <h1 class="page-title">{{ isNewUser ? '新建用户' : '用户详情' }}</h1>
      <div class="flex space-x-3">
        <router-link to="/admin/users" class="btn btn-secondary shadow-sm hover:shadow-md transition-all duration-200">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回列表
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-alert">
      <div class="flex">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 表单内容 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- 左侧：基本信息 -->
      <div class="md:col-span-2 space-y-8">
        <!-- 基本信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            基本信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="lastName" class="form-label">姓名 <span class="form-required">*</span></label>
                <input id="lastName" v-model="userForm.lastName" type="text" class="form-input" required
                  placeholder="请输入姓名" />
              </div>
              <div>
                <label for="email" class="form-label">邮箱 <span class="form-required">*</span></label>
                <input id="email" v-model="userForm.email" type="email" class="form-input" required
                  placeholder="请输入邮箱" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="phoneNumber" class="form-label">手机号码</label>
                <input id="phoneNumber" v-model="userForm.phoneNumber" type="tel" class="form-input"
                  placeholder="请输入手机号码" />
              </div>
              <div>
                <label for="isActive" class="form-label">状态</label>
                <select id="isActive" v-model="userForm.isActive" class="form-input">
                  <option :value="true">活跃</option>
                  <option :value="false">非活跃</option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="gender" class="form-label">性别</label>
                <select id="gender" v-model="userForm.gender" class="form-input">
                  <option :value="null">未指定</option>
                  <option value="male">男</option>
                  <option value="female">女</option>
                </select>
              </div>
              <div>
                <label for="idNumber" class="form-label">身份证号</label>
                <input id="idNumber" v-model="userForm.idNumber" type="text" class="form-input"
                  placeholder="请输入身份证号" />
              </div>
            </div>

            <div>
              <label for="address" class="form-label">地址</label>
              <textarea id="address" v-model="userForm.address" rows="2" class="form-input"
                placeholder="请输入地址"></textarea>
            </div>
          </div>
        </div>

        <!-- 角色和权限卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            角色和权限
          </h2>
          <div class="form-group">
            <div>
              <label for="role" class="form-label">角色</label>
              <select id="role" v-model="userForm.role" class="form-input">
                <option value="user">普通用户</option>
                <option value="admin">管理员</option>

              </select>
            </div>

          </div>
        </div>
      </div>

      <!-- 右侧：其他信息 -->
      <div class="space-y-8">
        <!-- 账号信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            账号信息
          </h2>
          <div class="form-group">
            <div>
              <label for="username" class="form-label">用户名 <span class="form-required">*</span></label>
              <input id="username" v-model="userForm.username" type="text" class="form-input" required
                placeholder="请输入用户名" />
            </div>
            <div v-if="isNewUser">
              <label for="password" class="form-label">密码 <span class="form-required">*</span></label>
              <input id="password" v-model="userForm.password" type="password" class="form-input" required
                placeholder="请输入密码" />
            </div>
            <div v-if="isNewUser">
              <label for="password_confirmation" class="form-label">确认密码 <span class="form-required">*</span></label>
              <input id="password_confirmation" v-model="userForm.password_confirmation" type="password" class="form-input" required
                placeholder="请再次输入密码" />
            </div>
          </div>
        </div>

        <!-- 其他信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            其他信息
          </h2>
          <div class="form-group">
            <div>
              <label for="departmentId" class="form-label">所属部门</label>
              <select id="departmentId" v-model="userForm.departmentId" class="form-input">
                <option value="">-- 请选择部门 --</option>
                <option v-for="dept in departments" :key="dept.value" :value="dept.value">
                  {{ dept.label }}
                </option>
              </select>
              <!-- 调试信息 -->
              <div v-if="departments.length === 0" class="text-xs text-red-500 mt-1">部门数据加载中或加载失败</div>
              <p v-if="departmentError" class="text-sm text-red-600 mt-1">{{ departmentError }}</p>
            </div>
            <div>
              <label for="position" class="form-label">职位</label>
              <input id="position" v-model="userForm.position" type="text" class="form-input"
                placeholder="请输入职位" />
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="flex justify-end mt-8">
      <button @click="saveUser" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isSaving">
        <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ isNewUser ? '创建' : '保存' }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import apiService from '@/services/apiService';
import axios from 'axios';
import notificationService from '@/services/notification.service';

const route = useRoute();
const router = useRouter();
const userId = computed(() => route.params.id);
const isNewUser = computed(() => userId.value === 'create');

// 状态变量
const loading = ref(false);
const error = ref('');
const isSaving = ref(false);
const departmentError = ref('');

// 表单数据
const userForm = reactive({
  username: '',
  email: '',
  password: '',
  password_confirmation: '',
  lastName: '',
  role: 'user',
  gender: null,
  departmentId: null,
  position: null,
  idNumber: null,
  phoneNumber: null,
  address: null,
  avatarUrl: null,
  isActive: true,
  isAdmin: false
});

// 部门列表
const departments = ref([]);

// 获取部门列表
const fetchDepartments = async () => {
  try {
    console.log('开始获取部门数据');
    departmentError.value = '';

    // 尝试使用部门服务获取数据
    try {
      // 导入部门服务
      const { useDepartmentStore } = await import('@/stores/department');
      const departmentStore = useDepartmentStore();

      // 强制刷新部门数据
      departmentStore.resetError();

      // 获取部门列表
      await departmentStore.fetchDepartments();

      // 从部门存储中获取部门选项
      departments.value = departmentStore.getDepartmentOptions;
      console.log('通过部门存储获取部门成功, 数量:', departments.value.length);
      console.log('部门数据:', JSON.stringify(departments.value, null, 2));
    } catch (storeError) {
      console.error('通过部门存储获取部门失败:', storeError);

      // 如果部门存储方法失败，尝试直接调用API
      // 获取API基础URL
      const apiBaseUrl = import.meta.env.VITE_API_URL || '';
      console.log('API基础URL:', apiBaseUrl);

      // 获取认证令牌
      const token = localStorage.getItem('token');
      console.log('认证令牌存在:', !!token);

      // 使用带有认证的请求
      console.log('发送请求到:', `${apiBaseUrl}/api/departments`);
      const response = await axios({
        method: 'get',
        url: `${apiBaseUrl}/api/departments`,
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        withCredentials: true
      });

      console.log('收到响应:', response.status, response.statusText);

      // 处理响应数据
      if (response.data && Array.isArray(response.data)) {
        departments.value = response.data.map(dept => ({
          value: dept.id,
          label: dept.name
        }));
        console.log('部门数据获取成功, 数量:', departments.value.length);
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // 处理嵌套在data字段中的数据
        departments.value = response.data.data.map(dept => ({
          value: dept.id,
          label: dept.name
        }));
        console.log('部门数据获取成功(嵌套格式), 数量:', departments.value.length);
      } else {
        console.warn('部门数据格式不符合预期:', response.data);
        departmentError.value = '部门数据格式不正确';
      }
    }
  } catch (err) {
    console.error('获取部门列表失败:', err);
    console.error('错误详情:', err.response?.data || err.message);
    departmentError.value = '获取部门列表失败';
  }
};

// 获取用户详情数据
async function fetchUserDetails() {
  // 如果是新用户，不需要获取数据
  if (isNewUser.value) {
    return;
  }

  try {
    console.log('Fetching user details for ID:', userId.value);

    // 从 API 获取数据
    const response = await apiService.getUserById(userId.value);

    console.log('API response received:', response);

    // 处理响应数据
    const userData = response.data || response;

    console.log('Processed user data:', userData);

    // 安全检查
    if (userData && typeof userData === 'object') {
      // 填充表单数据
      userForm.username = userData.username || '';
      userForm.email = userData.email || '';
      userForm.lastName = userData.lastName || '';
      userForm.role = userData.role || 'user';
      userForm.gender = userData.gender;
      userForm.departmentId = userData.departmentId;
      userForm.position = userData.position;
      userForm.idNumber = userData.idNumber;
      userForm.phoneNumber = userData.phoneNumber;
      userForm.address = userData.address;
      userForm.avatarUrl = userData.avatarUrl;
      userForm.isActive = userData.isActive === undefined ? true : userData.isActive;
      userForm.isAdmin = userData.isAdmin === undefined ? false : userData.isAdmin;

      console.log('表单数据已更新:', JSON.stringify({
        username: userForm.username,
        email: userForm.email,
        departmentId: userForm.departmentId,
        position: userForm.position
      }, null, 2));
    }
  } catch (err) {
    console.error('获取用户详情失败:', err);
    console.error('错误详情:', err.response?.data || err.message);
    error.value = '获取用户详情失败';
  }
}

onMounted(async () => {
  // 重置数据状态
  loading.value = true;
  error.value = '';
  departmentError.value = '';
  departments.value = [];

  // 重置表单数据，确保没有旧数据残留
  if (!isNewUser.value) {
    Object.keys(userForm).forEach(key => {
      if (typeof userForm[key] === 'string') {
        userForm[key] = '';
      } else if (typeof userForm[key] === 'boolean') {
        userForm[key] = false;
      } else {
        userForm[key] = null;
      }
    });
    // 恢复默认值
    userForm.isActive = true;
  }

  // 同时获取用户详情和部门列表
  try {
    await Promise.all([
      fetchUserDetails(),
      fetchDepartments()
    ]);
    console.log('数据加载完成');
  } catch (err) {
    console.error('数据加载失败:', err);
    error.value = '数据加载失败，请刷新页面重试';
  } finally {
    loading.value = false;
  }
});

// 保存用户信息
async function saveUser() {
  isSaving.value = true;
  error.value = '';

  try {
    // 验证必填字段
    if (!userForm.lastName?.trim()) {
      throw new Error('请输入姓名');
    }
    if (!userForm.email?.trim()) {
      throw new Error('请输入邮箱');
    }
    if (!userForm.username?.trim()) {
      throw new Error('请输入用户名');
    }
    if (isNewUser.value && !userForm.password?.trim()) {
      throw new Error('请输入密码');
    }
    if (isNewUser.value && userForm.password !== userForm.password_confirmation) {
      throw new Error('两次输入的密码不一致');
    }

    // 准备提交数据 - 只包含非常简单的字段，以解决验证错误
    const userData = {
      name: userForm.name,
      lastName: userForm.lastName,
      email: userForm.email,
      phoneNumber: userForm.phoneNumber,
      isActive: userForm.isActive,
      gender: userForm.gender,
      idNumber: userForm.idNumber,
      address: userForm.address,
      avatarUrl: userForm.avatarUrl,
      role: userForm.role,
      isAdmin: userForm.isAdmin,
      departmentId: userForm.departmentId || null,
      position: userForm.position || null
    };

    // 只在创建新用户时添加密码
    if (isNewUser.value) {
      userData.password = userForm.password;
    }

    // Debug: Log the data being sent
    console.log('Sending user data to server:', JSON.stringify(userData, null, 2));

    try {
      // 尝试使用apiService方法
      if (isNewUser.value) {
        console.log('创建用户中...');
        const response = await apiService.createUser(userData);
        console.log('Create user response:', response);
        handleSuccess('创建成功');
      } else {
        console.log('更新用户中...');
        const response = await apiService.updateUser(userId.value, userData);
        console.log('Update user response:', response);
        handleSuccess('保存成功');
      }
    } catch (apiError) {
      console.error('API Service方法失败，尝试直接调用API:', apiError);

      // 如果apiService方法失败，尝试直接调用
      try {
        // 获取API基础URL
        const apiBaseUrl = import.meta.env.VITE_API_URL || '';

        // 获取认证令牌
        const token = localStorage.getItem('token');

        if (isNewUser.value) {
          const response = await axios({
            method: 'post',
            url: `${apiBaseUrl}/api/users`,
            data: userData,
            headers: {
              'Content-Type': 'application/json',
              ...(token ? { 'Authorization': `Bearer ${token}` } : {})
            },
            withCredentials: true
          });
          console.log('Direct API create user response:', response);
          handleSuccess('创建成功');
        } else {
          // 尝试只修改指定ID的email字段
          const response = await axios({
            method: 'patch',
            url: `${apiBaseUrl}/api/users/${userId.value}`,
            data: userData,
            headers: {
              'Content-Type': 'application/json',
              ...(token ? { 'Authorization': `Bearer ${token}` } : {})
            },
            withCredentials: true
          });
          console.log('Direct API update user response:', response);
          handleSuccess('保存成功');
        }
      } catch (directApiError) {
        console.error('直接API调用也失败:', directApiError);
        console.error('Response data:', directApiError.response?.data);
        if (directApiError.response?.data?.message) {
          throw new Error(directApiError.response.data.message);
        } else if (directApiError.response?.data?.error?.message) {
          throw new Error(directApiError.response.data.error.message);
        } else if (directApiError.response?.data?.error) {
          throw new Error(directApiError.response.data.error);
        } else {
          throw directApiError;
        }
      }
    }
  } catch (err) {
    console.error('保存用户失败:', err);
    if (err.message) {
      error.value = err.message;
    } else if (err.response?.data?.message) {
      error.value = err.response.data.message;
    } else {
      error.value = '保存失败，请重试';
    }
    notificationService.error('保存失败', error.value);
  } finally {
    isSaving.value = false;
  }
}

// 处理成功保存的情况
function handleSuccess(message) {
  // 显示成功消息
  notificationService.success('保存成功', message);

  // 立即返回列表页
  router.replace({
    path: '/admin/users',
    query: {
      refresh: Date.now(), // 强制刷新列表
      page: 1 // 返回第一页
    }
  });
}
</script>