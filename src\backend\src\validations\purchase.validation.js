const Joi = require('joi');
const { objectId, dateFormat } = require('./custom.validation');

const createPurchase = {
  body: Joi.object().keys({
    purchaseNumber: Joi.string().required().messages({
      'string.empty': '采购编号不能为空',
      'any.required': '采购编号是必填项'
    }),
    projectId: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '供应商ID不能为空'
    }),
    status: Joi.string().valid('draft', 'pending', 'approved', 'rejected', 'completed', 'cancelled').messages({
      'any.only': '状态必须是草稿(draft)、待处理(pending)、已批准(approved)、已拒绝(rejected)、已完成(completed)或已取消(cancelled)'
    }),
    totalAmount: Joi.number().min(0).messages({
      'number.base': '总金额必须是数字',
      'number.min': '总金额不能小于{#limit}'
    }),
    currency: Joi.string().messages({
      'string.empty': '货币不能为空'
    }),
    purchaseDate: Joi.date().messages({
      'date.base': '采购日期必须是有效的日期格式'
    }),
    expectedDeliveryDate: Joi.date().allow(null).messages({
      'date.base': '预计交付日期必须是有效的日期格式'
    }),
    deliveryAddress: Joi.string().allow('', null),
    items: Joi.array().items(
      Joi.object().keys({
        name: Joi.string().required().messages({
          'string.empty': '物品名称不能为空',
          'any.required': '物品名称是必填项'
        }),
        quantity: Joi.number().required().min(1).messages({
          'number.base': '数量必须是数字',
          'number.min': '数量不能小于{#limit}',
          'any.required': '数量是必填项'
        }),
        unit: Joi.string().required().messages({
          'string.empty': '单位不能为空',
          'any.required': '单位是必填项'
        }),
        unitPrice: Joi.number().required().min(0).messages({
          'number.base': '单价必须是数字',
          'number.min': '单价不能小于{#limit}',
          'any.required': '单价是必填项'
        }),
        totalPrice: Joi.number().required().min(0).messages({
          'number.base': '总价必须是数字',
          'number.min': '总价不能小于{#limit}',
          'any.required': '总价是必填项'
        }),
        description: Joi.string().allow('', null)
      })
    ),
    notes: Joi.string().allow('', null),
    paymentTerms: Joi.string().allow('', null),
    attachments: Joi.array().items(
      Joi.object().keys({
        fileName: Joi.string().required().messages({
          'string.empty': '文件名不能为空',
          'any.required': '文件名是必填项'
        }),
        fileUrl: Joi.string().required().messages({
          'string.empty': '文件URL不能为空',
          'any.required': '文件URL是必填项'
        }),
        fileSize: Joi.number().messages({
          'number.base': '文件大小必须是数字'
        }),
        fileType: Joi.string().messages({
          'string.empty': '文件类型不能为空'
        })
      })
    )
  })
};

const getPurchases = {
  query: Joi.object().keys({
    purchaseNumber: Joi.string().messages({
      'string.empty': '采购编号不能为空'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    status: Joi.string().messages({
      'string.empty': '状态不能为空'
    }),
    startDate: Joi.date().custom(dateFormat).messages({
      'date.base': '开始日期必须是有效的日期格式',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
    }),
    endDate: Joi.date().custom(dateFormat).messages({
      'date.base': '结束日期必须是有效的日期格式',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
    }),
    sortBy: Joi.string().messages({
      'string.empty': '排序字段不能为空'
    }),
    limit: Joi.number().integer().messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    })
  })
};

const getPurchase = {
  params: Joi.object().keys({
    purchaseId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  })
};

const updatePurchase = {
  params: Joi.object().keys({
    purchaseId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  }),
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '供应商ID不能为空'
    }),
    status: Joi.string().valid('draft', 'pending', 'approved', 'rejected', 'completed', 'cancelled').messages({
      'any.only': '状态必须是草稿(draft)、待处理(pending)、已批准(approved)、已拒绝(rejected)、已完成(completed)或已取消(cancelled)'
    }),
    totalAmount: Joi.number().min(0).messages({
      'number.base': '总金额必须是数字',
      'number.min': '总金额不能小于{#limit}'
    }),
    currency: Joi.string().messages({
      'string.empty': '货币不能为空'
    }),
    purchaseDate: Joi.date().messages({
      'date.base': '采购日期必须是有效的日期格式'
    }),
    expectedDeliveryDate: Joi.date().allow(null).messages({
      'date.base': '预计交付日期必须是有效的日期格式'
    }),
    deliveryAddress: Joi.string().allow('', null),
    items: Joi.array().items(
      Joi.object().keys({
        name: Joi.string().required().messages({
          'string.empty': '物品名称不能为空',
          'any.required': '物品名称是必填项'
        }),
        quantity: Joi.number().required().min(1).messages({
          'number.base': '数量必须是数字',
          'number.min': '数量不能小于{#limit}',
          'any.required': '数量是必填项'
        }),
        unit: Joi.string().required().messages({
          'string.empty': '单位不能为空',
          'any.required': '单位是必填项'
        }),
        unitPrice: Joi.number().required().min(0).messages({
          'number.base': '单价必须是数字',
          'number.min': '单价不能小于{#limit}',
          'any.required': '单价是必填项'
        }),
        totalPrice: Joi.number().required().min(0).messages({
          'number.base': '总价必须是数字',
          'number.min': '总价不能小于{#limit}',
          'any.required': '总价是必填项'
        }),
        description: Joi.string().allow('', null)
      })
    ),
    notes: Joi.string().allow('', null),
    paymentTerms: Joi.string().allow('', null),
    attachments: Joi.array().items(
      Joi.object().keys({
        fileName: Joi.string().required().messages({
          'string.empty': '文件名不能为空',
          'any.required': '文件名是必填项'
        }),
        fileUrl: Joi.string().required().messages({
          'string.empty': '文件URL不能为空',
          'any.required': '文件URL是必填项'
        }),
        fileSize: Joi.number().messages({
          'number.base': '文件大小必须是数字'
        }),
        fileType: Joi.string().messages({
          'string.empty': '文件类型不能为空'
        })
      })
    )
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deletePurchase = {
  params: Joi.object().keys({
    purchaseId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  })
};

const approvePurchase = {
  params: Joi.object().keys({
    purchaseId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  })
};

const rejectPurchase = {
  params: Joi.object().keys({
    purchaseId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  }),
  body: Joi.object().keys({
    reason: Joi.string().required().messages({
      'string.empty': '拒绝原因不能为空',
      'any.required': '拒绝原因是必填项'
    })
  })
};

module.exports = {
  createPurchase,
  getPurchases,
  getPurchase,
  updatePurchase,
  deletePurchase,
  approvePurchase,
  rejectPurchase
}; 