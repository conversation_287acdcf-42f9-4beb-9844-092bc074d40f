<template>
  <header class="shadow" :style="{ backgroundColor: 'var(--bg-secondary)' }">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex px-2 lg:px-0">
          <div class="flex-shrink-0 flex items-center">
            <router-link to="/dashboard" class="text-xl font-bold" :style="{ color: 'var(--primary-color)' }">

            </router-link>
          </div>

        </div>

        <div class="flex items-center lg:hidden">
          <!-- Mobile menu button - removed since sidebar is always visible -->
        </div>

        <div class="flex lg:ml-4 lg:items-center">
          <!-- Theme Switcher -->
          <ThemeSwitcher class="mr-4" />

          <!-- 消息通知图标 -->
          <MessageNotificationIcon class="mr-4" />

          <div class="ml-4 relative flex-shrink-0">
            <div>
              <button @click="userMenuOpen = !userMenuOpen"
                      class="rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
                      :style="{ '--tw-ring-color': 'var(--primary-focus)' }">
                <span class="sr-only">Open user menu</span>
                <div v-if="authStore.user?.avatar" class="h-8 w-8 rounded-full">
                  <img class="h-8 w-8 rounded-full" :src="authStore.user.avatar" :alt="authStore.user.name" />
                </div>
                <div v-else class="h-8 w-8 rounded-full flex items-center justify-center text-white font-medium" :style="{ backgroundColor: 'var(--primary-color)' }">
                  {{ userInitials }}
                </div>
              </button>
            </div>

            <!-- User dropdown menu -->
            <div v-if="userMenuOpen"
                class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
                role="menu"
                aria-orientation="vertical"
                aria-labelledby="user-menu"
                :style="{ backgroundColor: 'var(--bg-secondary)' }">
              <div class="py-2 px-4 text-sm border-b border-gray-100" :style="{ color: 'var(--text-secondary)', borderColor: 'var(--secondary-border)' }">
                Signed in as<br>
                <span class="font-medium" :style="{ color: 'var(--text-primary)' }">{{ authStore.user?.email }}</span>
              </div>
          
    
       
              <button @click="logout" class="w-full text-left block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" :style="{ color: 'var(--text-secondary)' }">
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile menu - removed since sidebar is always visible -->
  </header>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useMenuStore } from '../stores/menu';
import ThemeSwitcher from './ThemeSwitcher.vue';
import DataSourceSwitcher from './DataSourceSwitcher.vue';
import MessageNotificationIcon from './MessageNotificationIcon.vue';

export default {
  name: 'AppHeader',
  components: {
    ThemeSwitcher,
    DataSourceSwitcher,
    MessageNotificationIcon
  },
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const menuStore = useMenuStore();

    const userMenuOpen = ref(false);

    // Load menu data
    onMounted(() => {
      menuStore.loadMenu();
    });

    // Calculate user initials for avatar placeholder
    const userInitials = computed(() => {
      if (!authStore.user?.name) return '?';

      return authStore.user.name
        .split(' ')
        .map(n => n[0])
        .join('')
        .toUpperCase()
        .substring(0, 2);
    });

    // Handle logout action
    const logout = async () => {
      try {
        await authStore.logout();
        router.push('/login');
        userMenuOpen.value = false;
      } catch (error) {
        console.error('Logout failed:', error);
      }
    };

    return {
      authStore,
      menuStore,
      userMenuOpen,
      userInitials,
      logout
    };
  }
};
</script>