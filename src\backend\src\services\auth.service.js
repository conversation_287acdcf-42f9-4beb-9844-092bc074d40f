const bcrypt = require('bcryptjs');
const httpStatus = require('http-status');
const tokenService = require('./token.service');
const ApiError = require('../utils/ApiError');
const { User, Token } = require('../models');

/**
 * Login with username and password
 * @param {string} email
 * @param {string} password
 * @returns {Promise<User>}
 */
const loginUserWithEmailAndPassword = async (email, password) => {
  const user = await User.findOne({ where: { email } });
  if (!user || !(await bcrypt.compare(password, user.password))) {
    throw new ApiError(401, 'Incorrect email or password');
  }
  if (!user.isActive) {
    throw new ApiError(401, 'User account is disabled');
  }
  return user;
};

/**
 * Logout
 * @param {string} refreshToken
 * @returns {Promise}
 */
const logout = async (refreshToken) => {
  const refreshTokenDoc = await Token.findOne({ where: { token: refreshToken, type: 'refresh', blacklisted: false } });
  if (!refreshTokenDoc) {
    throw new ApiError(404, 'Token not found');
  }
  await Token.update(
    { blacklisted: true },
    { where: { id: refreshTokenDoc.id } }
  );
};

/**
 * Refresh auth tokens
 * @param {string} refreshToken
 * @returns {Promise<Object>}
 */
const refreshAuth = async (refreshToken) => {
  try {
    const refreshTokenDoc = await tokenService.verifyToken(refreshToken, 'refresh');
    const user = await User.findByPk(refreshTokenDoc.userId);
    if (!user) {
      throw new Error();
    }
    await Token.update(
      { blacklisted: true },
      { where: { id: refreshTokenDoc.id } }
    );
    return tokenService.generateAuthTokens(user);
  } catch (error) {
    throw new ApiError(401, 'Please authenticate');
  }
};

/**
 * Reset password
 * @param {string} resetPasswordToken
 * @param {string} newPassword
 * @returns {Promise}
 */
const resetPassword = async (resetPasswordToken, newPassword) => {
  try {
    const resetPasswordTokenDoc = await tokenService.verifyToken(resetPasswordToken, 'resetPassword');
    const user = await User.findByPk(resetPasswordTokenDoc.userId);
    if (!user) {
      throw new Error();
    }
    await Token.update(
      { blacklisted: true },
      { where: { userId: user.id, type: 'resetPassword' } }
    );
    await updateUserById(user.id, { password: newPassword });
  } catch (error) {
    throw new ApiError(401, 'Password reset failed');
  }
};

/**
 * Verify email
 * @param {string} verifyEmailToken
 * @returns {Promise}
 */
const verifyEmail = async (verifyEmailToken) => {
  try {
    const verifyEmailTokenDoc = await tokenService.verifyToken(verifyEmailToken, 'verifyEmail');
    const user = await User.findByPk(verifyEmailTokenDoc.userId);
    if (!user) {
      throw new Error();
    }
    await Token.update(
      { blacklisted: true },
      { where: { userId: user.id, type: 'verifyEmail' } }
    );
    await updateUserById(user.id, { isEmailVerified: true });
  } catch (error) {
    throw new ApiError(401, 'Email verification failed');
  }
};

// Helper function to update user by ID - importing directly would cause circular dependency
const updateUserById = async (id, updateBody) => {
  const user = await User.findByPk(id);
  if (!user) {
    throw new ApiError(404, 'User not found');
  }
  // 移除密码哈希处理，依赖模型的beforeUpdate钩子
  // 密码哈希将由User模型的beforeUpdate钩子自动处理
  Object.assign(user, updateBody);
  await user.save();
  return user;
};

/**
 * Change password for logged in user
 * @param {string} userId
 * @param {string} currentPassword
 * @param {string} newPassword
 * @returns {Promise}
 */
const changePassword = async (userId, currentPassword, newPassword) => {
  const user = await User.findByPk(userId);
  if (!user) {
    throw new ApiError(404, 'User not found');
  }

  // Verify current password
  if (!(await bcrypt.compare(currentPassword, user.password))) {
    throw new ApiError(401, 'Current password is incorrect');
  }

  // Update password
  await updateUserById(userId, { password: newPassword });
};

module.exports = {
  loginUserWithEmailAndPassword,
  logout,
  refreshAuth,
  resetPassword,
  verifyEmail,
  changePassword,
};