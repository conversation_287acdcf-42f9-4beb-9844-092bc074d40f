const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createCustomer = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '客户名称不能为空',
      'any.required': '客户名称是必填项'
    }),
    category: Joi.string().allow(null, ''),
    address: Joi.string().allow(null, ''),
    phone: Joi.string().allow(null, ''),
    contactName: Joi.string().allow(null, ''),
    contactPhone: Joi.string().allow(null, ''),
    contactEmail: Joi.string().email().allow(null, '').messages({
      'string.email': '联系人邮箱格式不正确'
    }),
    followUpStage: Joi.string().allow(null, ''),
    followUpContent: Joi.string().allow(null, ''),
    tags: Joi.array().items(Joi.string()).allow(null),
    level: Joi.string().allow(null, ''),
    notes: Joi.string().allow(null, '')
  })
};

const getCustomer = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  })
};

const updateCustomer = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '客户名称不能为空'
    }),
    category: Joi.string().allow(null, ''),
    address: Joi.string().allow(null, ''),
    phone: Joi.string().allow(null, ''),
    contactName: Joi.string().allow(null, ''),
    contactPhone: Joi.string().allow(null, ''),
    contactEmail: Joi.string().email().allow(null, '').messages({
      'string.email': '联系人邮箱格式不正确'
    }),
    followUpStage: Joi.string().allow(null, ''),
    followUpContent: Joi.string().allow(null, ''),
    tags: Joi.array().items(Joi.string()).allow(null),
    level: Joi.string().allow(null, ''),
    notes: Joi.string().allow(null, '')
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteCustomer = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '客户ID不能为空',
      'any.required': '客户ID是必填项'
    })
  })
};

const getCustomers = {
  query: Joi.object().keys({
    category: Joi.string().messages({
      'string.empty': '类别不能为空'
    }),
    level: Joi.string().messages({
      'string.empty': '级别不能为空'
    }),
    followUpStage: Joi.string().messages({
      'string.empty': '跟进阶段不能为空'
    }),
    search: Joi.string().allow(null),
    tags: Joi.alternatives().try(
      Joi.string().allow(null),
      Joi.array().items(Joi.string())
    ),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
    limit: Joi.number().integer().messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数'
    }),
    sortBy: Joi.string().messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

module.exports = {
  createCustomer,
  getCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomers
}; 